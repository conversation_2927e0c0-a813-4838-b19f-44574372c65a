'use client'

import React, {
  useEffect, useRef, useState,
} from 'react';
import * as fabric from 'fabric';
import autosize from 'autosize';
import { VectorImageStyleGroups } from '@/common/constants';
import { getPath } from '@/common/utils/helpers';
import toast from 'react-hot-toast';
import {
  ArrowLeft, Download,
} from 'lucide-react';
import ImageTracer from 'imagetracerjs';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import {
  Button, TextArea,
} from '../../../atoms';

interface VectorImagePanelProps {
  canvas: fabric.Canvas | null;
  agentId?: string;
  planId?: string;
  containerRef?: React.RefObject<HTMLDivElement>;
  zoomLevel?: number;
}



export const VectorImagePanel = ({
  canvas,
  agentId,
  planId,
  containerRef,
  zoomLevel,
}: VectorImagePanelProps) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<typeof VectorImageStyleGroups.none.styles[0] | null>(null);
  const [seed, setSeed] = useState(Math.floor(Math.random() * 1000000));
  const [guidanceScale, setGuidanceScale] = useState(3.5);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState<'style' | 'details'>('style');
  const [svgPreview, setSvgPreview] = useState<string | null>(null);
  const { trackContentEvent } = useMixpanelEvent();
  const { activeProject } = useProjectContext();

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (style: typeof VectorImageStyleGroups.none.styles[0]) => {
    setSelectedStyle(style);
    setCurrentStep('details');
  };

  const handleBackToStyles = () => {
    setCurrentStep('style');
  };

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setIsGenerating(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

      const requestBody: any = {
        description: imagePrompt,
        planId: planId || 'new-post',
        seed: seed,
        width: 832,
        height: 832,
        steps: 40,
        guidanceScale: guidanceScale,
      };

      if (selectedStyle && selectedStyle.option !== 'none') {
        requestBody.style = [selectedStyle.option];
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 422 && errorData.error?.includes('NSFW')) {
          throw new Error('Content flagged as inappropriate. Please try a different prompt.');
        }
        throw new Error(errorData.error || 'Failed to generate image');
      }

      const imageData = await response.json();

      if (imageData && imageData.filepath) {
        const imageUrl = getPath(imageData.filepath);

        // Automatically vectorize the generated image
        await vectorizeAndAddToCanvas(imageUrl);

        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle?.option || 'none',
        });

        toast.success('Vector image generated and added to canvas!');
      } else {
        throw new Error('No image data received');
      }
    } catch (error: any) {
      console.error('Error generating image:', error);
      setError(error.message || 'Failed to generate image. Please try again.');
      toast.error(error.message || 'Failed to generate image');
    } finally {
      setIsGenerating(false);
    }
  };

  const removeBackground = (imageData: ImageData): ImageData => {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;

    // Create a new ImageData object
    const newImageData = new ImageData(width, height);
    const newData = newImageData.data;

    // Copy original data
    for (let i = 0; i < data.length; i++) {
      newData[i] = data[i];
    }

    // Enhanced background detection using edge pixels
    const getPixelIndex = (x: number, y: number) => (y * width + x) * 4;

    // Sample edge pixels to find the most common background color
    const edgePixels: number[][] = [];

    // Top and bottom edges
    for (let x = 0; x < width; x += Math.max(1, Math.floor(width / 20))) {
      const topIdx = getPixelIndex(x, 0);
      const bottomIdx = getPixelIndex(x, height - 1);
      edgePixels.push([data[topIdx], data[topIdx + 1], data[topIdx + 2]]);
      edgePixels.push([data[bottomIdx], data[bottomIdx + 1], data[bottomIdx + 2]]);
    }

    // Left and right edges
    for (let y = 0; y < height; y += Math.max(1, Math.floor(height / 20))) {
      const leftIdx = getPixelIndex(0, y);
      const rightIdx = getPixelIndex(width - 1, y);
      edgePixels.push([data[leftIdx], data[leftIdx + 1], data[leftIdx + 2]]);
      edgePixels.push([data[rightIdx], data[rightIdx + 1], data[rightIdx + 2]]);
    }

    // Find the most common color among edge pixels
    const colorCounts = new Map<string, { count: number; rgb: number[] }>();

    edgePixels.forEach(([r, g, b]) => {
      // Group similar colors together with tolerance
      const key = `${Math.floor(r / 10)}_${Math.floor(g / 10)}_${Math.floor(b / 10)}`;
      if (colorCounts.has(key)) {
        colorCounts.get(key)!.count++;
      } else {
        colorCounts.set(key, { count: 1, rgb: [r, g, b] });
      }
    });

    // Get the most frequent background color
    let maxCount = 0;
    let bgColor = [255, 255, 255]; // default to white

    for (const [, { count, rgb }] of colorCounts) {
      if (count > maxCount) {
        maxCount = count;
        bgColor = rgb;
      }
    }

    const [bgR, bgG, bgB] = bgColor;

    // Remove background with adaptive tolerance
    const tolerance = 40; // Increased tolerance for better background removal

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = getPixelIndex(x, y);
        const r = data[idx];
        const g = data[idx + 1];
        const b = data[idx + 2];

        // Check if pixel is close to background color
        const diff = Math.abs(r - bgR) + Math.abs(g - bgG) + Math.abs(b - bgB);

        if (diff <= tolerance) {
          // Make pixel transparent
          newData[idx + 3] = 0;
        }
      }
    }

    return newImageData;
  };

  const vectorizeAndAddToCanvas = async (imageUrl: string) => {
    try {
      // Load the image
      const img = new window.Image();
      img.crossOrigin = 'anonymous';

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = imageUrl;
      });

      // Create a temporary canvas to get image data
      const tempCanvas = document.createElement('canvas');
      const ctx = tempCanvas.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get canvas context');
      }

      tempCanvas.width = img.width;
      tempCanvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      // Get image data for vectorization
      let imageData = ctx.getImageData(0, 0, img.width, img.height);

      // Remove background before vectorization
      imageData = removeBackground(imageData);

      // Vectorize the image using ImageTracer with optimized settings for vector graphics
      const svgString = ImageTracer.imagedataToSVG(imageData, {
        ltres: 1,
        qtres: 1,
        pathomit: 8,
        colorsampling: 1,
        numberofcolors: 16,
        mincolorratio: 0.02,
        colorquantcycles: 3,
        blurradius: 0,
        blurdelta: 20,
      });

      setSvgPreview(svgString);

      // Automatically add to canvas
      await addSvgToCanvas(svgString);

    } catch (error: any) {
      console.error('Error vectorizing image:', error);
      setError(error.message || 'Failed to vectorize image');
      toast.error('Failed to vectorize image');
    }
  };

  const addSvgToCanvas = async (svgString: string) => {
    if (!canvas) {
      return;
    }

    try {
      // Create a blob from the SVG string
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // Load SVG into fabric canvas
      fabric.loadSVGFromURL(svgUrl).then((result: any) => {
        const { objects, options } = result;
        const svgGroup = fabric.util.groupSVGElements(objects, options);

        // Scale to fit canvas if needed
        if (canvas && containerRef?.current) {
          const containerRect = containerRef.current.getBoundingClientRect();
          const maxWidth = (containerRect.width * 0.8) / (zoomLevel || 1);
          const maxHeight = (containerRect.height * 0.8) / (zoomLevel || 1);

          if (svgGroup.width! > maxWidth || svgGroup.height! > maxHeight) {
            const scale = Math.min(maxWidth / svgGroup.width!, maxHeight / svgGroup.height!);
            svgGroup.scale(scale);
          }
        }

        canvas.add(svgGroup);
        canvas.centerObject(svgGroup);
        canvas.setActiveObject(svgGroup);
        canvas.renderAll();

        // Clean up the blob URL
        URL.revokeObjectURL(svgUrl);

        // Store in project images
        if (activeProject?.project_id && agentId) {
          const fileName = `Vector Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
          projectImageStorage.addGeneratedImage(
            activeProject.project_id,
            agentId,
            svgUrl,
            fileName,
            planId,
            imagePrompt,
          ).then(() => {
            window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
          }).catch((error) => {
            console.error('Error storing vector image:', error);
          });
        }

        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle?.option || 'none',
        });
      });
    } catch (error: any) {
      console.error('Error adding SVG to canvas:', error);
      toast.error('Failed to add vector image to canvas');
    }
  };

  const downloadSVG = () => {
    if (!svgPreview) return;

    const blob = new Blob([svgPreview], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `vector-image-${Date.now()}.svg`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="px-6 py-4">
      {currentStep === 'style' ? (
        <>
          <div className="mb-4">
            <h3 className="text-white font-semibold text-lg">Choose Vector Style</h3>
            <p className="text-gray-400 text-sm">Create vector images using AI with SVG output</p>
          </div>
          <div className="flex flex-col gap-4">
            <div className='flex-1'>
              <div className=" pr-2">
                {Object.entries(VectorImageStyleGroups).map(([groupKey, group], groupIndex) => (
                  <div key={groupKey} className={`${groupIndex > 0 ? 'mt-8' : ''} mb-6`}>
                    <div className="sticky top-0 pt-4 bg-neutral-900 pb-2 mb-3 z-10">
                      <h4 className="text-gray-300 text-xs font-semibold uppercase tracking-wide border-b border-neutral-700 pb-2">
                        {group.title}
                      </h4>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      {group.styles.map((style) => (
                        <button
                          key={style.option}
                          onClick={() => handleStyleSelect(style)}
                          className={`p-3 rounded-xl border-2 transition-all duration-200 text-left ${
                            selectedStyle?.option === style.option
                              ? 'border-violets-are-blue bg-violets-are-blue/10'
                              : 'border-neutral-600 hover:border-violets-are-blue bg-neutral-800 hover:bg-neutral-700'
                          }`}
                        >
                          <div className="text-white text-sm font-medium mb-1">
                            {style.label}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : currentStep === 'details' ? (
        <>
          <div className="mb-4 flex items-center gap-3">
            <Button
              onClick={handleBackToStyles}
              variant="outline"
              size="sm"
              className="!px-2"
            >
              <ArrowLeft size={16} />
            </Button>
            <div>
              <h3 className="text-white font-semibold text-lg">Describe Your Vector Image</h3>
              <p className="text-gray-400 text-sm">
                Style: <span className="text-violets-are-blue">{selectedStyle?.label}</span>
              </p>
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Image Description
              </label>
              <TextArea
                id="vector-image-prompt"
                name="vector-image-prompt"
                ref={imagePromptRef}
                value={imagePrompt}
                onChange={(e) => setImagePrompt(e.target.value)}
                placeholder="Describe the vector image you want to create..."
                className="w-full min-h-[100px] resize-none"
                maxLength={500}
              />
              <div className="flex justify-between items-center mt-1">
                <span className="text-xs text-gray-500">
                  {imagePrompt.length}/500 characters
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Seed: {seed}
                </label>
                <input
                  type="range"
                  min="0"
                  max="1000000"
                  step="1"
                  value={seed}
                  onChange={(e) => setSeed(Number(e.target.value))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Guidance: {guidanceScale}
                </label>
                <input
                  type="range"
                  min="1"
                  max="10"
                  step="0.1"
                  value={guidanceScale}
                  onChange={(e) => setGuidanceScale(Number(e.target.value))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
            </div>

            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            <Button
              variant="gradient"
              size="md"
              width="w-full"
              onClick={handleGenerate}
              disabled={isGenerating || !imagePrompt.trim()}
            >
              {isGenerating ? 'Generating and Vectorizing...' : 'Generate Vector Image'}
            </Button>
          </div>

          {svgPreview && (
            <div className="mt-6">
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Vector Preview
              </label>
              <div className="bg-neutral-800 rounded-lg p-3 border border-neutral-700">
                <div
                  className="w-full max-h-64 overflow-auto bg-white rounded"
                  dangerouslySetInnerHTML={{ __html: svgPreview }}
                />
              </div>
              <div className="flex gap-3 mt-3">
                <Button
                  variant="outline"
                  size="md"
                  onClick={downloadSVG}
                  className="flex-1"
                >
                  <Download size={16} className="mr-2" />
                  Download SVG
                </Button>
              </div>
            </div>
          )}
        </>
      ) : null}
    </div>
  );
};
